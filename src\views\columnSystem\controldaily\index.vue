
<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item :label="$t('columnSystem.mdorm')" prop="mdorm">
        <el-input
          v-model="queryParams.mdorm"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.nindex')" prop="nindex">
        <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('columnSystem.mname')" prop="mname">
        <el-input
          v-model="queryParams.mname"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="cyan"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >{{ $t("common.search") }}</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("common.reset")
        }}</el-button>
      </el-form-item>
    </el-form>
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="3" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            :placeholder="$t('columnSystem.pleaseEnterDeptName')"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="deptOptions"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            default-expand-all
            @node-click="handleNodeClick"
          />
        </div>
      </el-col>
      <!--用户数据-->
      <el-col :span="21" :xs="24">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['columnSystem:controldaily:add']"
              >{{ $t("common.add") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['columnSystem:controldaily:edit']"
              >{{ $t("common.update") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['columnSystem:controldaily:remove']"
              >{{ $t("common.delete") }}</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <!-- <el-button
              type="warning"
              icon="el-icon-download"
              size="mini"
              @click="handleExport"
              v-hasPermi="['columnSystem:controldaily:export']"
              >{{ $t("common.export") }}</el-button
            > -->
          </el-col>
          <!-- <el-col :span="1.5">
            <el-button
              type="primary"
              icon="el-icon-setting"
              size="mini"
              :disabled="ruleType"
              @click="handleSet"
              v-hasPermi="['columnSystem:controldaily:set']"
              >参数下发</el-button
            >
          </el-col> -->

          <right-toolbar
            :showSearch.sync="showSearch"
            @queryTable="getList"
          ></right-toolbar>
          <el-col :span="1.5" style="margin: 0 auto">
            <el-tooltip
              :content="$t('columnSystem.onlyShowControl')"
              placement="top"
            >
              <el-switch
                style="
                  position: absolute;
                  top: 50%;
                  transform: translateY(-50%);
                "
                v-model="viewSwitch"
                active-color="#409eff"
                inactive-color="#dcdfe6"
                active-value="3"
                inactive-value="0"
                @change="changeSwitch"
              >
              </el-switch>
            </el-tooltip>
          </el-col>
        </el-row>

        <el-table
          v-loading="loading"
          :data="controldailyList"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column
            :label="$t('columnSystem.topologyNodeType')"
            align="center"
            prop="ntype"
            :formatter="typeFormatter"
          />
          <el-table-column
            :label="$t('columnSystem.mdorm')"
            align="center"
            prop="mdorm"
          />
          <el-table-column
            :label="$t('columnSystem.nindex')"
            align="center"
            prop="nindex"
          />
          <el-table-column
            :label="$t('columnSystem.mname')"
            align="center"
            prop="mname"
          />
          <el-table-column
            :label="$t('columnSystem.naddress')"
            align="center"
            prop="naddress"
          />
          <el-table-column
            :label="$t('columnSystem.naddressup')"
            align="center"
            prop="naddressup"
          />
          <el-table-column
            :label="$t('columnSystem.nserial')"
            align="center"
            prop="nserial"
          />
          <el-table-column
            :label="$t('columnSystem.nstatus')"
            align="center"
            prop="nstatus"
          />
          <el-table-column
            :label="$t('columnSystem.iszeromodel')"
            align="center"
            prop="iszeromodel"
            :formatter="iszero"
          />
          <!-- <el-table-column label="激活状态: 0未激活 1已激活 2有故障" align="center" prop="nactivate" />-->

          <el-table-column
            :label="$t('columnSystem.nworktype')"
            align="center"
            prop="nworktype"
            :formatter="workModeFormat"
          />
          <el-table-column
            :label="$t('columnSystem.ndirect')"
            align="center"
            prop="ndirect"
            :formatter="doorDirectionFormat"
          />
          <el-table-column
            :label="$t('columnSystem.nsceen')"
            align="center"
            prop="nsceen"
          />
          <el-table-column
            :label="$t('columnSystem.ntrend')"
            align="center"
            prop="ntrend"
          />
          <el-table-column
            :label="$t('columnSystem.ntimeopen')"
            align="center"
            prop="ntimeopen"
          />
          <!-- <el-table-column label="分栏门关闭时间: 0.2-5秒，默认0.5秒" align="center" prop="ntimeclose" /> -->
          <el-table-column
            :label="$t('columnSystem.ntimereopen')"
            align="center"
            prop="ntimereopen"
          />
          <el-table-column
            :label="$t('columnSystem.ntimedelay')"
            align="center"
            prop="ntimedelay"
          />
          <!-- <el-table-column label="称重延时时间: 0-10秒，默认2秒" align="center" prop="nweightdelay" />  -->
          <el-table-column
            :label="$t('columnSystem.nweighttime')"
            align="center"
            prop="nweighttime"
          />

          <el-table-column
            :label="$t('columnSystem.npignums')"
            align="center"
            prop="cunlannum"
          />
          <el-table-column
            :label="$t('common.operate')"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['columnSystem:controldaily:edit']"
                >{{ $t("common.update") }}</el-button
              >
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['columnSystem:controldaily:remove']"
                >{{ $t("common.delete") }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1200px"
      append-to-body
    >
      <el-form ref="form" :model="form" label-width="110px">
        <el-divider>{{ $t("columnSystem.basicInform") }}</el-divider>
        <el-row>
          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.topologyNodeType')"
                prop="ntype"
                :rules="rules.ntype"
              >
                <el-select
                  v-model="form.ntype"
                  :placeholder="$t('common.pleaseChoose')"
                  @change="changeNtype"
                >
                  <el-option
                    v-for="item in ntypeOptions"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.mdorm')"
                prop="mdorm"
                :rules="rules.mdorm"
              >
                <el-input
                  v-model="form.mdorm"
                  :placeholder="$t('common.pleaseInput')"
                  type="number"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.nindex')"
                prop="nindex"
                :rules="form.ntype === 1 ? [{ required: false }] : rules.nindex"
              >
                <el-input
                  v-model="form.nindex"
                  :placeholder="$t('common.pleaseInput')"
                  :disabled="form.ntype === 1"
                  type="number"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.mname')"
                prop="mname"
                type="number"
                :rules="
                  form.ntype === 1 || form.ntype === 2
                    ? [{ required: false }]
                    : rules.mname
                "
              >
                <el-input
                  v-model="form.mname"
                  :placeholder="$t('common.pleaseInput')"
                  :disabled="form.ntype === 1 || form.ntype === 2"
                /> </el-form-item
            ></el-col>
          </el-row>
          <!--  -->
          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.naddress')"
                prop="naddress"
                v-show="form.ntype === 3"
                :rules="
                  form.ntype === 1 || form.ntype === 2
                    ? [{ required: false }]
                    : rules.naddress
                "
              >
                <el-input
                  v-model="form.naddress"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.naddressup')"
                prop="naddressup"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.naddressup"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.nserial')"
                prop="nserial"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nserial"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.switchid')"
                prop="switchid"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.switchid"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.nstatus')"
                prop="nstatus"
                v-show="form.ntype === 3"
              >
                <el-radio-group v-model="form.nstatus">
                  <el-radio
                    v-for="dict in statusOptions"
                    :key="dict.dictValue"
                    :label="dict.dictValue"
                    >{{ dict.dictLabel }}</el-radio
                  >
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nversion')"
                prop="nversion"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nversion"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.npignums')"
                prop="cunlannum"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.cunlannum"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.mmemo')"
                prop="mmemo"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.mmemo"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <!-- <el-col :xs="6" :sm="6" :md="6" :lg="6"
                ><el-form-item label="激活状态" prop="nactivate" v-show="form.ntype === 3">
                  <el-radio-group v-model="form.nactivate">
                    <el-radio
                      v-for="dict in nativateOptions"
                      :key="dict.dictValue"
                      :label="dict.dictValue"
                      >{{ dict.dictLabel }}</el-radio
                    >
                  </el-radio-group>
                  </el-form-item
              ></el-col> -->
          </el-row>
          <el-divider v-if="form.ntype === 3">{{
            $t("columnSystem.timeParameter")
          }}</el-divider>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.ntimeopen')"
                prop="ntimeopen"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ntimeopen"
                  :placeholder="$t('columnSystem.pleaseEnterNtimeopen')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.ntimeclose')"
                prop="ntimeclose"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ntimeclose"
                  :placeholder="$t('columnSystem.pleaseEnterNtimeclose')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ntimereopen')"
                prop="ntimereopen"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ntimereopen"
                  :placeholder="$t('columnSystem.pleaseEnterNtimereopen')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ntimedelay')"
                prop="ntimedelay"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ntimedelay"
                  :placeholder="$t('columnSystem.pleaseEnterNtimedelay')"
                /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nweightdelay')"
                prop="nweightdelay"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nweightdelay"
                  :placeholder="$t('columnSystem.pleaseEnterNweightdelay')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nweighttime')"
                prop="nweighttime"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nweighttime"
                  :placeholder="$t('columnSystem.pleaseEnterNweighttime')"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-divider v-if="form.ntype === 3">{{
            $t("columnSystem.calibrationAndWeight")
          }}</el-divider>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nindivnull')"
                prop="nindivnull"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nindivnull"
                  :placeholder="$t('columnSystem.pleaseEnterNindivnull')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.nindivweight')"
                prop="nindivweight"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nindivweight"
                  :placeholder="$t('columnSystem.pleaseEnterNindivweight')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nindiv')"
                prop="nindiv"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nindiv"
                  :placeholder="$t('columnSystem.pleaseEnterNindiv')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nweightstart')"
                prop="nweightstart"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nweightstart"
                  :placeholder="$t('columnSystem.pleaseEnterNweightstart')"
                /> </el-form-item
            ></el-col>
          </el-row>

          <el-divider v-if="form.ntype === 3">{{
            $t("columnSystem.columnProperties")
          }}</el-divider>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nworktypeValue')"
                prop="nworktype"
                v-show="form.ntype === 3"
              >
                <el-select
                  v-model="form.nworktype"
                  :placeholder="$t('common.pleaseChoose')"
                >
                  <el-option
                    v-for="dict in workModeList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="Number(dict.dictValue)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.ndirectValue')"
                prop="ndirect"
                v-show="form.ntype === 3"
              >
                <el-select
                  v-model="form.ndirect"
                  :placeholder="$t('common.pleaseChoose')"
                >
                  <el-option
                    v-for="dict in doorDirectionList"
                    :key="dict.dictValue"
                    :label="dict.dictLabel"
                    :value="Number(dict.dictValue)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nsceenValue')"
                prop="nsceen"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nsceen"
                  :placeholder="$t('columnSystem.pleaseEnterNsceenValue')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ntrendValue')"
                prop="ntrend"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ntrend"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
          </el-row>

          <el-divider v-if="form.ntype === 3">{{
            $t("columnSystem.columns")
          }}</el-divider>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ngroupweight1')"
                prop="ngroupweight1"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ngroupweight1"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6"
              ><el-form-item
                :label="$t('columnSystem.ngroupweight2')"
                prop="ngroupweight2"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ngroupweight2"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ncolumnpct')"
                prop="ncolumnpct"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ncolumnpct"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ncolumnweight')"
                prop="ncolumnweight"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ncolumnweight"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
          </el-row>

          <el-divider v-if="form.ntype === 3">{{
            $t("columnSystem.fatteningClusters")
          }}</el-divider>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.ngroupweight3')"
                prop="ngroupweight3"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.ngroupweight3"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>

            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nmarketweight2')"
                prop="nmarketweight2"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nmarketweight2"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nmarketweight1')"
                prop="nmarketweight1"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nmarketweight1"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
          </el-row>

          <el-row :xs="24" :sm="24" :md="24" :lg="24">
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nlightpct')"
                prop="nlightpct"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nlightpct"
                  :placeholder="$t('common.pleaseInput')"
                />
              </el-form-item>
            </el-col>

            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nmidweight')"
                prop="nmidweight"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nmidweight"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
            <el-col :xs="6" :sm="6" :md="6" :lg="6">
              <el-form-item
                :label="$t('columnSystem.nheavypct')"
                prop="nheavypct"
                v-show="form.ntype === 3"
              >
                <el-input
                  v-model="form.nheavypct"
                  :placeholder="$t('common.pleaseInput')"
                /> </el-form-item
            ></el-col>
          </el-row>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          :loading="submitLoading"
          >{{ $t("common.determine") }}</el-button
        >
        <el-button @click="cancel">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="titleSet"
      :visible.sync="openSet"
      width="600px"
      append-to-body
      :destroy-on-close="true"
      :show-close="false"
      :before-close="cancelSet"
      :close="cancelSet"
      :closed="cancelSet"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formSet"
        :model="formSet"
        label-width="130px"
        v-loading="loadingSet"
      >
        <el-form-item :label="$t('columnSystem.mname')" prop="mname">
          <el-input
            v-model="formSet.mname"
            :placeholder="$t('common.pleaseInput')"
            disabled
          />
        </el-form-item>
        <el-form-item :label="$t('columnSystem.naddress')" prop="naddress">
          <el-input
            v-model="formSet.naddress"
            :placeholder="$t('common.pleaseInput')"
            disabled
          />
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.ncorrect')"
          prop="ncorrect"
          :rules="rulesSet.ncorrect"
        >
          <el-input
            v-model="formSet.ncorrect"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'ncorrect')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-255)</i
            >
          </el-input>
        </el-form-item>

        <el-form-item label="DPC" prop="ndpc" :rules="rulesSet.ndpc">
          <el-input
            v-model="formSet.ndpc"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'ndpc')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-1)kg</i
            >
          </el-input>
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.nsurplus')"
          prop="nsurplus"
          :rules="rulesSet.nsurplus"
        >
          <el-input
            v-model="formSet.nsurplus"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'nsurplus')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-1)kg</i
            >
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('columnSystem.npulse')"
          prop="npulse"
          :rules="rulesSet.npulse"
        >
          <el-input
            v-model="formSet.npulse"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'npulse')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-1)</i
            >
          </el-input>
        </el-form-item>
        <el-form-item
          :label="$t('columnSystem.nindiv')"
          prop="nindiv"
          :rules="rulesSet.nindiv"
        >
          <el-input
            v-model="formSet.nindiv"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'nindiv')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-255)</i
            >
          </el-input>
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.nindivnull')"
          prop="nindivnull"
          :rules="rulesSet.nindivnull"
        >
          <el-input
            v-model="formSet.nindivnull"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'nindivnull')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-100)kg</i
            >
          </el-input>
        </el-form-item>

        <el-form-item
          :label="$t('columnSystem.ncorrectnull')"
          prop="ncorrectnull"
          :rules="rulesSet.ncorrectnull"
        >
          <el-input
            v-model="formSet.ncorrectnull"
            :placeholder="$t('common.pleaseInput')"
            @change="handleDataSave($event, 'ncorrectnull')"
          >
            <i slot="suffix" style="font-style: normal; margin-right: 10px"
              >(0-100)kg</i
            >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitFormSet"
          :loading="loadingSet"
          >{{ $t("common.refresh") }}</el-button
        >
        <el-button @click="cancelSet">{{ $t("common.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { boarControlMqtt, checkTime } from "@/api/system/control";
import { treeselect } from "@/api/columnSystem/overview";
import {
  listControldaily,
  getControldaily,
  delControldaily,
  addControldaily,
  updateControldaily,
  exportControldaily,
} from "@/api/columnSystem/controldaily";
// import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { uuid } from "@/utils";
import { isUndefined, isEmpty, isObject } from "lodash";

let source;

export default {
  name: "Controldaily",
  components: {},
  data() {
    return {
      viewSwitch: "3",
      ntypeOptions: [
        {
          value: 1,
          name: this.$t("columnSystem.mdorm"),
        },
        {
          value: 2,
          name: this.$t("columnSystem.nindex"),
        },
        {
          value: 3,
          name: this.$t("columnSystem.mname"),
        },
      ],
      // 状态数据字典
      statusOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("boarMeasure.stop"),
        },
        {
          dictValue: 1,
          dictLabel: this.$t("boarMeasure.normal"),
        },
      ],
      // 激活状态数据字典
      nativateOptions: [
        {
          dictValue: 0,
          dictLabel: this.$t("boarMeasure.inactive"),
        },
        {
          dictValue: 1,
          dictLabel: this.$t("boarMeasure.active"),
        },
      ],
      // stausMap: {
      //   1: "猪舍号",
      //   2: "栏号",
      //   3: "分栏站",
      // },
      deptName: undefined,
      defaultProps: {
        children: "children",
        label: "label",
      },
      // 猪舍树选项
      deptOptions: undefined,
      // 遮罩层
      loading: true,
      loadingSet: true,
      // 选中数组
      ids: [],
      nowId: "",
      // 类型
      ruleType: true,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 【控制器配置】表格数据
      controldailyList: [],
      titleSet: "",
      openSet: false,
      // 弹出层标题
      title: "",
      submitLoading: false,
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mfactory: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: null,
        nversion: null,
        nactivate: null,
        ntimeopen: null,
        ntimeclose: null,
        ntimereopen: null,
        ntimedelay: null,
        nweightdelay: null,
        nweighttime: null,
        nworktype: null,
        ndirect: null,
        nsceen: null,
        ntrend: null,
        nindivnull: null,
        nindivweight: null,
        nindiv: null,
        nweightstart: null,
        ngroupweight1: null,
        ngroupweight2: null,
        ngroupweight3: null,
        nmarketweight1: null,
        nmarketweight2: null,
        ncolumnpct: null,
        ncolumnweight: null,
        noffsetweight: null,
        nlightpct: null,
        nmidweight: null,
        nheavypct: null,
        mmemo: null,
        ngroupweight4: null,
        npignums: null,
        cunlannum: null
      },
      // 表单参数
      form: {},
      // 参数下发表单
      formSet: {},
      // 表单校验
      rulesSet: {
        ncorrect: [
          {
            pattern: /^(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5]))))$/g,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            // required: true,
            // message: this.$t("common.notNull"),
            trigger: ["blur", "change"],
          },
        ],
        ndpc: [
          {
            pattern: /^(0(\.\d{1,3})?|1)$/g,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
        nsurplus: [
          {
            pattern: /^(0(\.\d{1,3})?|1)$/g,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
        npulse: [
          {
            pattern: /^[01]$/g,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
        nindiv: [
          {
            pattern: /^(([0-9]|([1-9]\d)|(1\d\d)|(2([0-4]\d|5[0-5]))))$/g,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
        nindivnull: [
          {
            pattern: /^(\d{1,2}(\.\d{1,3})?|100)$/,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
        ncorrectnull: [
          {
            pattern: /^(\d{1,2}(\.\d{1,3})?|100)$/,
            message: this.$t("columnSystem.pleaseEnterCorrectRange"),
            trigger: "blur",
          },
        ],
      },
      rules: {
        ntype: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        mdorm: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        nindex: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        mname: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
        naddress: [
          {
            required: true,
            message: this.$t("common.notNull"),
            trigger: "blur",
          },
        ],
      },
      workModeList: [],
      doorDirectionList: [],
    };
  },
  watch: {
    queryParams: {
      handler() {
        this.getList();
      },
      deep: true,
    },
    // viewSwitch: {
    //   handler() {
    //     this.getList();
    //   },
    //   deep: true,
    // },
  },

  created() {
    this.getList();
    this.getTreeselect();
    this.getDicts("fl_work_mode").then((response) => {
      this.workModeList = response.data;
    });
    this.getDicts("fl_door_direction").then((response) => {
      this.doorDirectionList = response.data;
    });
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
  },
  beforeDestroy() {
    if (
      !(typeof source == "undefined" || source == undefined || source == null)
    ) {
      source.close();
      source = null;
    }
  },
  methods: {
    workModeFormat(row, column) {
      return this.selectDictLabel(this.workModeList, row.nworktype);
    },

    iszero(row, column) {
      if(row.iszeromodel == 1)
        return this.$t("columnSystem.notzeromodel");
      else if(row.iszeromodel == 2)
        return this.$t("columnSystem.zeromodel");
    },

    doorDirectionFormat(row, column) {
      return this.selectDictLabel(this.doorDirectionList, row.ndirect);
    },
    handleCheckTime(row) {
      checkTime({
        indexn: row.indexn,
        switchid: row.switchid,
        naddress: row.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        this.msgSuccess(this.$t("columnSystem.timingSuccess"));
        // this.open = false;
        // this.getList();
        // this.getTreeselect();
      });
    },
    changeNtype(val) {
      if (val === 1) {
        this.form.nindex = null;
        this.form.mname = null;
        // this.form.mdorm = Number(nowForm.mdorm) + 1;
      } else if (val === 2) {
        this.form.mname = null;
        // this.form.nindex = Number(nowForm.nindex) + 1;
      }
      // else if (val === 3) {
      //   this.form.mname = Number(nowForm.mname) + 1;
      // }
    },
    valueFormat(row, column, cellValue, index) {
      return cellValue / 1000;
    },
    /** 查询【控制器配置】列表 */
    // 字典状态字典翻译
    statusFormat(row, column) {
      return this.selectDictLabel(this.statusOptions, row.nstatus);
    },
    nativateFormat(row, column) {
      return this.selectDictLabel(this.nativateOptions, row.nactivate);
    },

    typeFormatter(row) {
      return (
        this.ntypeOptions.filter((item) => item.value === row.ntype) &&
        this.ntypeOptions.filter((item) => item.value === row.ntype)[0].name
      );
    },
    // 节点单击事件
    handleNodeClick(data) {
      // this.queryParams.pageNum = 1;
      // this.queryParams.indexn = data.id;
      // this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      // this.getList();
      this.queryParams.mname = null;
      this.queryParams.nindex = null;
      this.queryParams.mdorm = null;
      listControldaily({
        pageNum: 1,
        indexn: data.id,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        // ntype类型  mdorm猪舍号 nindex栏号 mname分栏站
        this.queryParams.pageNum = 1;
        this.queryParams.indexn = data.id;
        this.queryParams.ntype = response.rows[0].ntype;
        if (response.rows && response.rows[0].ntype == 3) {
          this.queryParams.mname = response.rows[0].mname;
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 2) {
          this.queryParams.nindex = response.rows[0].nindex;
          this.queryParams.mdorm = response.rows[0].mdorm;
        } else if (response.rows && response.rows[0].ntype == 1) {
          this.queryParams.mdorm = response.rows[0].mdorm;
        }
        this.getList();

        // this.controldailyList = response.rows;
        // this.total = response.total;

        // this.queryParams.pageNum = 1;
        this.loading = false;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /** 查询猪舍结构树 */
    getTreeselect() {
      treeselect({ mfactory: this.$store.state.settings.nowPigFarm }).then(
        (response) => {
          this.deptOptions = response.data;
        }
      );
    },
    //控制器配置
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      this.queryParams.ntype = this.viewSwitch == 3 ? 3 : null;

      listControldaily(this.queryParams).then((response) => {
        this.controldailyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 取消按钮
    cancelSet() {
      if (
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
      }
      this.openSet = false;
      this.resetSet();
    },
    //参数下发单个保存
    handleDataSave(value, type) {
      // ndpc nsurplus ncorrectnull nindivnull
      // /system/boarcontrolmqtt------put方法，设置单个控制数据
      // 参数switchid,naddress,mfactory,indexn
      boarControlMqtt({
        // indexn: this.nowId,
        indexn: this.formSet.indexn,
        switchid: this.formSet.switchid,
        naddress: this.formSet.naddress,
        mfactory: this.$store.state.settings.nowPigFarm,
        [type]:
          type == "ndpc" ||
          type == "nsurplus" ||
          type == "ncorrectnull" ||
          type == "nindivnull"
            ? value * 1000
            : value,
      }).then((response) => {
        this.msgSuccess(this.$t("common.modifiedSuccess"));
        // this.open = false;
        // this.getList();
        // this.getTreeselect();
      });
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mfactory: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: 0,
        nversion: null,
        nactivate: null,
        ntimeopen: null,
        ntimeclose: null,
        ntimereopen: null,
        ntimedelay: null,
        nweightdelay: null,
        nweighttime: null,
        nworktype: null,
        ndirect: null,
        nsceen: null,
        ntrend: null,
        nindivnull: null,
        nindivweight: null,
        nindiv: null,
        nweightstart: null,
        ngroupweight1: null,
        ngroupweight2: null,
        ngroupweight3: null,
        nmarketweight1: null,
        nmarketweight2: null,
        ncolumnpct: null,
        ncolumnweight: null,
        noffsetweight: null,
        nlightpct: null,
        nmidweight: null,
        nheavypct: null,
        mmemo: null,
        ngroupweight4: null,
        npignums: null,
        cunlannum: null
      };
      this.resetForm("form");
    },

    // 表单重置
    resetSet() {
      this.formSet = {
        indexn: null,
        ntype: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        naddressup: null,
        nserial: null,
        nstatus: 0,
        nversion: null,
        nactivate: null,
        ncorrect: null,
        ncorrectmin: null,
        ncorrectmax: null,
        ndpc: null,
        ndpcmin: null,
        ndpcmax: null,
        nsurplus: null,
        npulse: null,
        nindiv: null,
        nindivmin: null,
        nindivmax: null,
        nindivnull: null,
        ncorrectnull: null,
        dstartdate: null,
        denddate: null,
        nweight1: null,
        nweight2: null,
        mmemo: null,
      };
      this.resetForm("formSet");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.deptName = undefined;
      this.queryParams.indexn = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id);
      this.single = selection.length !== 1;
      this.ruleType = !(selection.length === 1 && selection[0].ntype === 3);

      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("columnSystem.addControl");
    },
    //参数下发
    async handleSet(row) {
      this.resetSet();
      const indexn = row.id || this.ids;
      this.nowId = indexn;
      this.formSet.naddress = 1;
      // ndpc nsurplus ncorrectnull nindivnull
      getControldaily(indexn).then((response) => {
        this.formSet = response.data;
        this.formSet.ndpc = this.formSet.ndpc / 1000;
        this.formSet.nsurplus = this.formSet.nsurplus / 1000;
        this.formSet.ncorrectnull = this.formSet.ncorrectnull / 1000;
        this.formSet.nindivnull = this.formSet.nindivnull / 1000;
      });
      if (
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
      }
      await this.handleEventSource(indexn);
      this.openSet = true;
      this.titleSet = this.$t("columnSystem.parameterDistribution");
    },
    handleEventSource(indexn) {
      //创建SSE对象 查询
      let timer = null;
      if (window.EventSource) {
        // 创建 EventSource 对象连接服务器
        //const source = new EventSource('http://localhost:2000');
        // source = new EventSource("http://localhost:2000/");
        source = new EventSource(
          process.env.VUE_APP_BASE_API +
            `/system/sse/subscribe?uuid=${uuid(
              12,
              10
            )}&indexn=${indexn}&mfactory=${
              this.$store.state.settings.nowPigFarm
            }`
        );

        // 连接成功后会触发 open 事件
        source.addEventListener("open", (e) => {}, false);

        // 服务器发送信息到客户端时，如果没有 event 字段，默认会触发 message 事件
        source.addEventListener(
          "message",
          (e) => {
            if (e.data === "查询中") {
              this.loadingSet = true;
              const that = this;
              timer = setTimeout(function () {
                that.msgError(this.$t("columnSystem.requestTimedOut"));
                that.loadingSet = false;
              }, 30000);
            } else if (
              JSON.parse(e.data) &&
              isObject(JSON.parse(e.data)) &&
              JSON.stringify(JSON.parse(e.data)) !== "{}"
            ) {
              clearTimeout(timer);
              this.formSet.ncorrect = JSON.parse(e.data).ncorrect;
              this.formSet.ndpc = JSON.parse(e.data).ndpc / 1000;
              this.formSet.nsurplus = JSON.parse(e.data).nsurplus / 1000;
              this.formSet.npulse = JSON.parse(e.data).npulse;
              this.formSet.nindiv = JSON.parse(e.data).nindiv;
              this.formSet.ncorrectnull =
                JSON.parse(e.data).ncorrectnull / 1000;
              this.formSet.nindivnull = JSON.parse(e.data).nindivnull / 1000;
              this.loadingSet = false;
              if (
                !(
                  typeof source == "undefined" ||
                  source == undefined ||
                  source == null
                )
              ) {
                source.close();
                source = null;
              }
            }
          },
          false
        );

        // 自定义 EventHandler，在收到 event 字段为 slide 的消息时触发
        source.addEventListener("slide", (e) => {}, false);

        // 连接异常时会触发 error 事件并自动重连
        source.addEventListener(
          "error",
          (e) => {
            if (e.target.readyState === EventSource.CLOSED) {
              source.close();
              source = null;
              this.msgError(this.$t("columnSystem.requestTimedOut"));
              this.loadingSet = false;
              console.log("Disconnected");
            } else if (e.target.readyState === EventSource.CONNECTING) {
              source.close();
              source = null;
              // this.msgError(this.$t("columnSystem.requestTimedOut"));
              this.loadingSet = false;
              console.log("Connecting...");
            }
          },
          false
        );
        // }
      } else {
        console.error("Your browser doesn't support SSE");
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.id || this.ids;
      getControldaily(indexn).then((response) => {
        this.form = response.data;
        //ndpcmin ndpcmax nindivmin nindivmax
        // this.form.ndpc = this.form.ndpc && this.form.ndpc / 1000;
        // this.form.nsurplus = this.form.nsurplus && this.form.nsurplus / 1000;
        // this.form.ncorrectnull =
        //   this.form.ncorrectnull && this.form.ncorrectnull / 1000;
        // this.form.nindivnull =
        //   this.form.nindivnull && this.form.nindivnull / 1000;
        // this.form.ndpcmin = this.form.ndpcmin && this.form.ndpcmin / 1000;
        // this.form.ndpcmax = this.form.ndpcmax && this.form.ndpcmax / 1000;
        // this.form.nweight1 = this.form.nweight1 && this.form.nweight1 / 1000;
        // this.form.nweight2 = this.form.nweight2 && this.form.nweight2 / 1000;
        this.open = true;
        this.title = this.$t("columnSystem.updateControl");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.submitLoading = true;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          let nowForm = {
            ...this.form,
            mfactory: this.$store.state.settings.nowPigFarm,
            // ndpc: this.form.ndpc && this.form.ndpc * 1000,
            // nsurplus: this.form.nsurplus && this.form.nsurplus * 1000,
            // ncorrectnull:
            //   this.form.ncorrectnull && this.form.ncorrectnull * 1000,
            // nindivnull: this.form.nindivnull && this.form.nindivnull * 1000,
            // ndpcmin: this.form.ndpcmin && this.form.ndpcmin * 1000,
            // ndpcmax: this.form.ndpcmax && this.form.ndpcmax * 1000,
            // nweight1: this.form.nweight1 && this.form.nweight1 * 1000,
            // nweight2: this.form.nweight2 && this.form.nweight2 * 1000,
          };

          if (this.form.id != null) {
            updateControldaily(nowForm).then((response) => {
              if (response) {
                this.msgSuccess(this.$t("common.modifiedSuccess"));
                this.submitLoading = false;
                this.open = false;
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          } else {
            addControldaily(nowForm).then((response) => {
              if (response) {
                this.msgSuccess(this.$t("common.addSuccess"));
                this.submitLoading = false;
                // this.open = false;
                if (nowForm.ntype === 1) {
                  this.form.mdorm = Number(nowForm.mdorm) + 1;
                } else if (nowForm.ntype === 2) {
                  this.form.nindex = Number(nowForm.nindex) + 1;
                } else if (nowForm.ntype === 3) {
                  this.form.mname = Number(nowForm.mname) + 1;
                }
                this.getList();
                this.getTreeselect();
              } else {
                this.submitLoading = false;
              }
            });
            this.submitLoading = false;
          }
        }
      });
    },
    submitFormSet() {
      this.loadingSet = true;
      if (
        !(typeof source == "undefined" || source == undefined || source == null)
      ) {
        source.close();
        source = null;
        // delete source;
      }
      this.handleEventSource(this.nowId);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.id || this.ids;
      this.$confirm(
        this.$t("columnSystem.sureCancelControl") +
          `"` +
          indexns +
          `"` +
          this.$t("common.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delControldaily(indexns);
        })
        .then(() => {
          this.getList();
          this.getTreeselect();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm(
        this.$t("columnSystem.sureExportControl"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportControldaily(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    changeSwitch(val) {
      this.getList();
    },
  },
};
</script>
