<template>
  <div class="app-container">
    <el-row>
      <el-col :span="24" class="card-box">
        <!-- <el-card> -->
        <!-- <div slot="header"><span>搜索参数</span></div> -->
        <el-form
          :model="queryParams"
          ref="queryForm"
          :inline="true"
          v-show="showSearch"
          label-width="68px"
        >
          <el-form-item
            :label="$t('boarMeasure.mensurationNow')"
            prop="checked"
          >
            <el-checkbox v-model="queryParams.checked" />
          </el-form-item>
          <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
            <!-- <el-input
          v-model="queryParams.nindex"
          :placeholder="$t('common.pleaseInput')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        /> -->
            <span>
              <el-select
                v-model="queryParams.nindex"
                :placeholder="$t('common.pleaseChoose')"
                size="small"
                @change="listPigdata"
              >
                <el-option
                  v-for="item in nIndexOptions"
                  :key="item.nindex"
                  :label="item.nindex"
                  :value="item.nindex"
                  @click="listPigdata"
                ></el-option>
              </el-select>

              <div
                style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                  :class="{ icon: iconShowSmall }"
                  class="el-icon-caret-top"
                  style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                  @click="handleSmall"
                ></i>
                <i
                  :class="{ icon: iconShowBig }"
                  class="el-icon-caret-bottom"
                  style="font-size: 22px; position: absolute; color: #c0c4cc"
                  @click="handleBig"
                ></i>
              </div>
            </span>
          </el-form-item>
          <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
            <!-- <el-input
              v-model="queryParams.mid"
              :placeholder="$t('common.pleaseInput')"
              clearable
              size="small"
              @change="handleMid"
            /> -->
            <span>
              <el-select
                v-model="queryParams.mid"
                @change="handleMid"
                :placeholder="$t('common.pleaseChoose')"
                clearable
                filterable
                allow-create
                size="small"
              >
                <el-option
                  v-for="(item, index) in mrfidOptions"
                  :key="index"
                  :label="item.mid"
                  :value="item.mid"
                ></el-option>
              </el-select>
              <div
                style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                  :class="{ icon: iconShowSmall }"
                  class="el-icon-caret-top"
                  style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                  @click="handleSmallMid"
                ></i>
                <i
                  :class="{ icon: iconShowBig }"
                  class="el-icon-caret-bottom"
                  style="font-size: 22px; position: absolute; color: #c0c4cc"
                  @click="handleBigMid"
                ></i>
              </div>
            </span>
          </el-form-item>
          <!-- <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
            <span>
              <el-select
                v-model="queryParams.mrfid"
                :placeholder="$t('common.pleaseChoose')"
                clearable
                filterable
                size="small"
              >
                <el-option
                  v-for="(item, index) in mrfidOptions"
                  :key="index"
                  :label="item.mrfid"
                  :value="item.mrfid"
                  @click.native="handleMrfid"
                ></el-option>
              </el-select>
              <div
                style="
                  position: relative;
                  display: inline-block;
                  width: 20px;
                  height: 10px;
                "
              >
                <i
                  :class="{ icon: iconShowSmall }"
                  class="el-icon-caret-top"
                  style="
                    font-size: 22px;
                    position: absolute;
                    top: -16px;
                    color: #c0c4cc;
                  "
                  @click="handleSmallRfid"
                ></i>
                <i
                  :class="{ icon: iconShowBig }"
                  class="el-icon-caret-bottom"
                  style="font-size: 22px; position: absolute; color: #c0c4cc"
                  @click="handleBigRfid"
                ></i>
              </div>
            </span>
          </el-form-item> -->
          <!-- 查询条件选择 -->
          <el-form-item label="查询条件" class="full-line">
            <el-radio-group
              v-model="activeQueryType"
              @change="handleQueryTypeChange"
            >
              <el-radio label="date"> 默认查询 </el-radio>
              <el-radio label="age">
                {{ $t("boarMeasure.rlrange") }}
              </el-radio>
              <el-radio label="weight">
                {{ $t("boarMeasure.tizhongfanwei") }}
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 日龄范围 -->
          <el-form-item
            v-if="activeQueryType === 'age'"
            :label="$t('boarMeasure.rlrange')"
            prop="ageRange"
          >
            <div
              class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange el-range-editor--small"
            >
              <input
                v-model="queryParams.rlFrom"
                :placeholder="$t('boarMeasure.startrl')"
                class="el-range-input"
                @input="handleAgeRangeInput"
                @blur="validateAgeRange"
              />
              <span class="el-range-separator">-</span>
              <input
                v-model="queryParams.rlTo"
                :placeholder="$t('boarMeasure.endrl')"
                class="el-range-input"
                @input="handleAgeRangeInput"
                @blur="validateAgeRange"
              />
            </div>
          </el-form-item>

          <!-- 体重范围 -->
          <el-form-item
            v-if="activeQueryType === 'weight'"
            :label="$t('boarMeasure.tizhongfanwei')"
            prop="weightRange"
          >
            <div
              class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange el-range-editor--small"
            >
              <input
                v-model="queryParams.weightFrom"
                :placeholder="$t('boarMeasure.startWeight')"
                class="el-range-input"
                @input="handleWeightRangeInput"
                @blur="validateWeightRange"
              />
              <span class="el-range-separator">-</span>
              <input
                v-model="queryParams.weightTo"
                :placeholder="$t('boarMeasure.endWeight')"
                class="el-range-input"
                @input="handleWeightRangeInput"
                @blur="validateWeightRange"
              />
            </div>
          </el-form-item>
          <el-form-item
            :label="$t('boarMeasure.dateOfDetermination')"
            prop="dateRange"
          >
            <el-date-picker
              v-model="queryParams.dateRange"
              size="small"
              style="width: 240px"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              @input="changeDate"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              type="cyan"
              icon="el-icon-search"
              size="mini"
              @click="getPigDataByMid"
              >{{ $t("common.search") }}</el-button
            >
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
              $t("common.reset")
            }}</el-button>
          </el-form-item>
        </el-form>
        <!-- </el-card> -->
        <el-card>
          <el-descriptions :title="$t('boarMeasure.basicInformation')">
            <el-descriptions-item :label="$t('boarMeasure.nindex')"
              ><span v-if="pigdata">{{
                pigdata.nindex
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.mid')"
              ><span v-if="pigdata">{{
                pigdata.mid
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.mrfid')"
              ><span v-if="pigdata">{{
                pigdata.mrfid
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.startWeight')">
              <span v-if="pigdata">{{ firstweight | formatNumber(1) }}</span>
            </el-descriptions-item>
            <el-descriptions-item :label="$t('boarMeasure.measureDays')">{{
              measureDays
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('boarMeasure.recordDays')">{{
              recordDays
            }}</el-descriptions-item>
            <el-descriptions-item :label="$t('boarMeasure.allNIngestionS')">
              <span v-if="allNIngestionS">{{
                allNIngestionS | formatNumber(3)
              }}</span></el-descriptions-item
            >

            <el-descriptions-item :label="$t('boarMeasure.weightGrow')">
              <span v-if="weightGrow">{{
                weightGrow | formatNumber(1)
              }}</span></el-descriptions-item
            >
            <el-descriptions-item :label="$t('boarMeasure.liaoRouBi')">
              <span v-if="weightGrow && allNIngestionS">{{
                (allNIngestionS / weightGrow) | formatNumber(2)
              }}</span></el-descriptions-item
            >

            <el-descriptions-item :label="$t('boarMeasure.averageDailyGrowth')">
              <span v-if="weightGrow && measureDays">{{
                (weightGrow / measureDays) | formatNumber(2)
              }}</span></el-descriptions-item
            >
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane
              :label="$t('boarMeasure.dailyData')"
              name="first"
              :lazy="true"
              style="padding-bottom: 10px"
            >
              <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    size="mini"
                    @click="handleAdd"
                    v-hasPermi="['boarMeasure:measureday:add']"
                    >{{ $t("common.add") }}</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="success"
                    icon="el-icon-edit"
                    size="mini"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['boarMeasure:measureday:edit']"
                    >{{ $t("common.update") }}</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="danger"
                    icon="el-icon-delete"
                    size="mini"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['boarMeasure:measureday:remove']"
                    >{{ $t("common.delete") }}</el-button
                  >
                </el-col>
                <el-col :span="1.5">
                  <el-button
                    type="warning"
                    icon="el-icon-download"
                    size="mini"
                    @click="handleExport"
                    v-hasPermi="['boarMeasure:measureday:export']"
                    >{{ $t("common.export") }}</el-button
                  >
                </el-col>
                <right-toolbar
                  :showSearch.sync="showSearch"
                  @queryTable="getList"
                ></right-toolbar>
              </el-row>
              <el-table
                v-loading="loading"
                :data="measuredayList"
                @selection-change="handleSelectionChange"
                @row-click="clickTr"
                :cell-style="{ padding: '0' }"
              >
                <el-table-column type="selection" width="55" align="center" />
                <!-- <el-table-column label="序号" align="center" prop="indexn" /> -->
                <el-table-column
                  :label="$t('boarMeasure.mrfid')"
                  align="center"
                  prop="mrfid"
                  width="180"
                  sortable
                />
                <!-- <el-table-column :label="$t('boarMeasure.mdorm')" align="center" prop="mdorm" />
                <el-table-column :label="$t('boarMeasure.nindex')" align="center" prop="nindex" />
                <el-table-column :label="$t('boarMeasure.mname')" align="center" prop="mname" />
                <el-table-column
                  :label="$t('boarMeasure.naddress')"
                  align="center"
                  prop="naddress"
                /> -->
                <el-table-column
                  :label="$t('boarMeasure.dateOfDetermination')"
                  align="center"
                  prop="ndate"
                  width="180"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.ndate, "{y}-{m}-{d}") }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.ningestions')"
                  align="right"
                  prop="ningestions"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{
                      scope.row.ningestions | formatThousands(0)
                    }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.weightKg')"
                  align="right"
                  prop="nmidweight"
                  sortable
                  ><template slot-scope="scope">
                    <span>{{ scope.row.nmidweight | formatNumber(1) }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  :label="$t('boarMeasure.nfeednum')"
                  align="right"
                  prop="nfeednum"
                  sortable
                />
                <el-table-column
                  :label="$t('boarMeasure.nsecond')"
                  align="right"
                  prop="nseconds"
                  sortable
                >
                  <template slot-scope="scope">
                    <span>{{ scope.row.nseconds | secondsToHMS }}</span>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                v-show="total > 0"
                :total="total"
                :page.sync="queryParams.pageNum"
                :limit.sync="queryParams.pageSize"
                @pagination="getPigDataByMid"
              />
              <el-dialog
                :title="titleMeasure"
                :visible.sync="openMeasure"
                width="1500"
                append-to-body
              >
                <el-row :gutter="10" class="mb8">
                  <el-col :span="1.5">
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      size="mini"
                      @click="handleGather"
                      v-hasPermi="['boarMeasure:measureday:gather']"
                      >{{ $t("boarMeasure.manualSummary") }}</el-button
                    >
                  </el-col>
                  <!-- <el-col :span="1.5">
                    <el-button
                      type="primary"
                      icon="el-icon-plus"
                      size="mini"
                      @click="handleAddData"
                      v-hasPermi="['boarMeasure:measureday:addData']"
                      >新增</el-button
                    >
                  </el-col> -->
                </el-row>
                <el-table
                  v-loading="loading"
                  :data="measureList"
                  :cell-style="{ padding: '0' }"
                >
                  <!-- <el-table-column label="序号" align="center" prop="indexn" /> -->
                  <el-table-column
                    :label="$t('boarMeasure.mid')"
                    align="center"
                    prop="mid"
                    width="180"
                  />
                  <el-table-column
                    :label="$t('boarMeasure.mrfid')"
                    align="center"
                    prop="mrfid"
                    width="180"
                  />
                  <!-- <el-table-column
                    :label="$t('boarMeasure.mdorm')"
                    align="center"
                    prop="mdorm"
                    width="60"
                  />
                  <el-table-column
                    :label="$t('boarMeasure.nindex')"
                    align="center"
                    prop="nindex"
                    width="50"
                  />
                  <el-table-column
                    :label="$t('boarMeasure.mname')"
                    align="center"
                    prop="mname"
                    width="60"
                  />
                  <el-table-column
                    :label="$t('boarMeasure.naddress')"
                    align="center"
                    prop="naddress"
                    width="75"
                  /> -->
                  <el-table-column
                    :label="$t('boarMeasure.intakesTime')"
                    align="center"
                    prop="nenddate"
                    width="180"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <!-- <el-input
                          size="mini"
                          v-model="scope.row.nenddate"
                        ></el-input> -->
                        <el-date-picker
                          clearable
                          size="small"
                          style="width: 200px"
                          v-model="scope.row.nenddate"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :placeholder="$t('common.pleaseChoose')"
                        >
                        </el-date-picker>
                      </span>

                      <span v-else>{{ parseTime(scope.row.nenddate) }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.feedingInterval')"
                    align="right"
                    prop="nseconds"
                    width="110"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.nseconds"
                        ></el-input>
                      </span>
                      <span v-else>{{
                        scope.row.nseconds | formatThousands(0)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.nfriweight')"
                    align="right"
                    prop="nfriweight"
                    width="80"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.nfriweight"
                          @blur="
                            formatRowToThreeDecimals(scope.row, 'nfriweight')
                          "
                        ></el-input>
                      </span>
                      <span v-else>{{
                        scope.row.nfriweight | formatThousands(0)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.nsecweight')"
                    align="right"
                    prop="nsecweight"
                    width="80"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.nsecweight"
                          @blur="
                            formatRowToThreeDecimals(scope.row, 'nsecweight')
                          "
                        ></el-input>
                      </span>
                      <span v-else>
                        {{ scope.row.nsecweight | formatThousands(0) }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.ningestionG')"
                    align="right"
                    prop="ningestion"
                    width="80"
                  >
                    <template slot-scope="scope">
                      <span>{{
                        scope.row.ningestion | formatThousands(0)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.weightKg')"
                    align="right"
                    prop="nweight"
                    width="80"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.nweight"
                        ></el-input>
                      </span>
                      <span v-else>{{
                        scope.row.nweight | formatNumber(1)
                      }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.nnum')"
                    align="right"
                    prop="nnum"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.nnum"
                        ></el-input>
                      </span>
                      <span v-else>{{ scope.row.nnum }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    :label="$t('boarMeasure.DPC')"
                    align="center"
                    prop="ndpc"
                    width="100"
                  >
                    <template slot-scope="scope">
                      <span v-if="scope.row.show">
                        <el-input
                          size="mini"
                          v-model="scope.row.ndpc"
                        ></el-input>
                      </span>
                      <span v-else>{{ scope.row.ndpc }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('common.operate')" width="150">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="text"
                        :icon="
                          scope.row.show ? 'el-icon-close' : 'el-icon-edit'
                        "
                        @click="editDate(scope.row, scope.$index)"
                        v-hasPermi="['boarMeasure:measureday:editData']"
                        >{{
                          scope.row.show
                            ? $t("common.cancel")
                            : $t("common.update")
                        }}</el-button
                      >
                      <el-button
                        size="mini"
                        type="text"
                        :icon="scope.row.show ? 'el-icon-check' : ''"
                        @click="
                          handleDataSave(scope.row, scope.$index, measureList)
                        "
                        v-hasPermi="['boarMeasure:measureday:saveData']"
                        >{{
                          scope.row.show ? $t("common.save") : ""
                        }}</el-button
                      >
                      <el-button
                        size="mini"
                        type="text"
                        :icon="scope.row.show ? '' : 'el-icon-delete'"
                        @click="deleteDate(scope.row, scope.$index)"
                        v-hasPermi="['boarMeasure:measureday:deleteData']"
                        >{{
                          scope.row.show ? "" : $t("common.delete")
                        }}</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>

                <pagination
                  v-show="totalMeasure > 0"
                  :total="totalMeasure"
                  :page.sync="queryParamsMeasure.pageNum"
                  :limit.sync="queryParamsMeasure.pageSize"
                  @pagination="getListMeasure"
                />
              </el-dialog>
              <el-dialog
                :title="$t('boarMeasure.manualSummary')"
                :visible.sync="openDayGather"
                width="500px"
                append-to-body
              >
                <!-- this.queryParamsMeasure.nstartdate -->
                <el-form
                  ref="formDayGather"
                  :model="formDayGather"
                  label-width="130px"
                >
                  <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
                    <el-input v-model="formDayGather.mid" :disabled="true">
                    </el-input>
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.manualSummaryDate')"
                    prop="nenddate"
                  >
                    <el-date-picker
                      size="small"
                      style="width: 200px"
                      v-model="formDayGather.nenddate"
                      type="date"
                      disabled
                      value-format="yyyy-MM-dd HH:mm:ss"
                      :placeholder="$t('common.pleaseChoose')"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="submitFormDayGather">{{
                    $t("common.determine")
                  }}</el-button>
                  <el-button @click="cancelDayGather">{{
                    $t("common.cancel")
                  }}</el-button>
                </div>
              </el-dialog>
              <!-- 添加或修改【个体测定信息】对话框 -->
              <el-dialog
                :title="title"
                :visible.sync="open"
                width="500px"
                append-to-body
              >
                <el-form
                  ref="form"
                  :model="form"
                  :rules="rules"
                  label-width="80px"
                >
                  <el-form-item :label="$t('boarMeasure.mid')" prop="mid">
                    <el-input
                      v-model="form.mid"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('boarMeasure.mrfid')" prop="mrfid">
                    <el-input
                      v-model="form.mrfid"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('boarMeasure.mdorm')" prop="mdorm">
                    <el-input
                      v-model="form.mdorm"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item :label="$t('boarMeasure.nindex')" prop="nindex">
                    <el-input
                      v-model="form.nindex"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>

                  <el-form-item
                    :label="$t('boarMeasure.naddress')"
                    prop="naddress"
                  >
                    <el-input
                      v-model="form.naddress"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.dateOfDetermination')"
                    prop="ndate"
                  >
                    <el-date-picker
                      clearable
                      size="small"
                      style="width: 200px"
                      v-model="form.ndate"
                      type="date"
                      value-format="yyyy-MM-dd"
                      :placeholder="$t('common.pleaseChoose')"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.ningestions')"
                    prop="ningestions"
                  >
                    <el-input
                      v-model="form.ningestions"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.nfeednum')"
                    prop="nfeednum"
                  >
                    <el-input
                      v-model="form.nfeednum"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.nseconds')"
                    prop="nseconds"
                  >
                    <el-input
                      v-model="form.nseconds"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('boarMeasure.weightMid')"
                    prop="nmidweight"
                  >
                    <el-input
                      v-model="form.nmidweight"
                      :placeholder="$t('common.pleaseInput')"
                    />
                  </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                  <el-button type="primary" @click="submitForm">{{
                    $t("common.determine")
                  }}</el-button>
                  <el-button @click="cancel">{{
                    $t("common.cancel")
                  }}</el-button>
                </div>
              </el-dialog>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('boarMeasure.ningestion')"
              name="second"
              :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category22" id="category22" style="height: 400px" />
              </div>
            </el-tab-pane>
            <el-tab-pane
              :label="$t('boarMeasure.weight')"
              name="third"
              :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category23" id="category23" style="height: 400px" />
              </div>
            </el-tab-pane>
            <!-- <el-tab-pane
              :label="$t('boarMeasure.dailyGainWeight')"
              name="four"
              :lazy="true"
            >
              <div class="el-table el-table--enable-row-hover el-table--medium">
                <div ref="category24" id="category24" style="height: 400px" />
              </div>
            </el-tab-pane> -->
            <!--
            <el-tab-pane :label="$t('boarMeasure.dataAggregation')" name="five">
              <el-card style="margin-bottom: 15px">
                <el-descriptions :title="$t('boarMeasure.ningestion')">
                  <el-descriptions-item
                    :label="$t('boarMeasure.allNIngestionS')"
                    ><span v-if="allNIngestionS">{{
                      Math.round(allNIngestionS)
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item :label="$t('boarMeasure.recordDays')"
                    ><span v-if="recordDays">{{
                      recordDays
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('boarMeasure.averageDailyFeedIntake')"
                  >
                    <span v-if="allNIngestionS && recordDays">{{
                      (allNIngestionS / recordDays).toFixed(2)
                    }}</span></el-descriptions-item
                  >
                </el-descriptions>
              </el-card>

              <el-card>
                <el-descriptions :title="$t('boarMeasure.weight')">
                  <el-descriptions-item :label="$t('boarMeasure.startWeight')"
                    ><span v-if="firstweight">{{
                      firstweight
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item :label="$t('boarMeasure.nearWeight')"
                    ><span v-if="lastweight">{{
                      lastweight
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item :label="$t('boarMeasure.weightGrow')"
                    ><span v-if="weightGrow">{{
                      weightGrow
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item :label="$t('boarMeasure.measureDays')"
                    ><span v-if="recordDays">{{
                      recordDays
                    }}</span></el-descriptions-item
                  >
                  <el-descriptions-item
                    :label="$t('boarMeasure.averageDailyGrowth')"
                    ><span v-if="weightGrow && recordDays">{{
                      (weightGrow / recordDays).toFixed(2)
                    }}</span></el-descriptions-item
                  >
                </el-descriptions>
              </el-card>
            </el-tab-pane> -->
          </el-tabs>
        </el-card>
      </el-col>
      <!-- <el-col :span="10" class="card-box">
        <el-card>
          <div slot="header">
            <span>采食量</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category22" id="category22" style="height: 300px" />
          </div>
        </el-card>
        <el-card>
          <div slot="header">
            <span>体重</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <div ref="category23" id="category23" style="height: 300px" />
          </div>
        </el-card>
      </el-col> -->
    </el-row>
  </div>
</template>

<script>
import {
  listMeasureday,
  getdaterangebyweightrange,
  getMeasureday,
  getFoodCharts,
  getWeightCharts,
  getPigweightGrowCharts,
  delMeasureday,
  addMeasureday,
  updateMeasureday,
  exportMeasureday,
} from "@/api/system/measureday";
import { gatherOneDayOnePigData } from "@/api/system/measureday";
import * as echarts from "echarts";
require("@/utils/walden"); // echarts theme
import {
  listPigdata,
  getdaterangebyrl,
  getPigdata,
  delPigdata,
  addPigdata,
  updatePigdata,
  exportPigdata,
} from "@/api/system/pigdata";
import {
  formatDate,
  formatDay,
  getDaysDiffBetweenDates,
  addTime,
} from "@/utils";
import { listControl } from "@/api/system/control";
import {
  listMeasure,
  getMeasure,
  delMeasure,
  addMeasure,
  updateMeasure,
  exportMeasure,
} from "@/api/system/measure";
import { isNil, isEmpty } from "lodash";
import Vue from "vue";

export default {
  name: "Measureday",
  components: {},
  props: {
    iconShowSmall: {
      type: Boolean,
      default: true,
    },
    iconShowBig: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      // defaultDateRange: [],
      // defaultDateRange: [new Date("2022-01-01"), new Date("2022-01-07")], // 默认日期范围
      openDayGather: false,
      formDayGather: {},
      //电子耳牌数组
      mrfidOptions: [],
      //栏号数组
      nIndexOptions: [],
      // 防抖定时器
      rlRangeTimer: null,
      weightRangeTimer: null,
      activeName: "first",
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 【测定日报告】表格数据
      measuredayList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //测定天数
      measureDays: null,
      //记录天数
      recordDays: null,
      //总采食量
      allNIngestionS: null,
      //总增重
      weightGrow: null,
      lastweight: null,
      firstweight: null,

      pigdata: null,
      //采食echart
      category22: null,
      //体重echart
      category23: null,
      //日增重echart
      category24: null,
      //采食x轴数据
      xDataFood: [],
      //采食y轴数据
      yDataFood: [],
      //体重x轴数据
      xWeight: [],
      //体重y轴数据
      yWeight: [],
      //日增重x轴数据
      xWeightGrow: [],
      //日增重y轴数据
      yWeightGrow: [],
      //foodEchart
      foodEchartList: [],
      //weightEchart
      weightEchartList: [],
      weightGrowList: [],
      measureList: [],
      oldMeasureList: [],
      totalMeasure: 0,
      openMeasure: false,
      titleMeasure: "",
      // 查询参数
      queryParamsMeasure: {
        pageNum: 1,
        pageSize: 100,
        nindex: null,
        mrfid: null,
        ntype: null,
      },
      // 激活的查询类型（单选）
      activeQueryType: "date",
      // 防抖定时器
      rlRangeTimer: null,
      weightRangeTimer: null,
      queryParams: {
        checked: true,
        pageNum: 1,
        pageSize: 100,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nmidweight: null,
        dateRange: null,
        weightFrom: null,
        weightTo: null,
        rlFrom: null,
        rlTo: null,
      },
      // 表单参数
      form: {},
      // 表单校验

      rules: {
        // mid: [{ required: true, message: "耳缺号不能为空", trigger: "blur" }],
        mrfid: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mdorm: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        nindex: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        mname: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        naddress: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        ndate: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        ningestions: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        nfeednum: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        nseconds: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
        nmidweight: [
          {
            required: true,
            message: this.$t("boarMeasure.notNull"),
            trigger: "blur",
          },
        ],
      },
    };
  },
  watch: {
    "queryParams.checked": {
      handler() {
        if (this.queryParams && this.queryParams.mid) {
          this.getPigDataByMid();
        }
      },
      deep: true,
    },
    "queryParams.nindex": {
      handler() {
        if (this.queryParams && this.queryParams.mid) {
          this.getPigDataByMid();
        }
      },
      deep: true,
    },
    "queryParams.mid": {
      handler() {
        if (this.queryParams && this.queryParams.mid) {
          this.getPigDataByMid();
        }
      },
      deep: true,
    },
    "queryParams.dateRange": {
      handler() {
        if (this.queryParams && this.queryParams.mid) {
          this.getPigDataByMid();
        }
      },
      deep: true,
    },
  },
  created() {
    this.restoreQueryParams();
    // this.initializeData();
  },
  // activated() {
  //   // 页面被激活时恢复查询条件
  //   this.restoreQueryParams();
  // },
  // deactivated() {
  //   // 页面被缓存时保存查询条件
  //   this.saveQueryParams();
  // },
  beforeDestroy() {
    // 页面被缓存时保存查询条件
    this.saveQueryParams();
  },
  mounted() {
    //渲染之后
    //this.getDatas();
    window.onresize = () => {
      //alert("sss");
      // this.category22.resize(); //重新初始化echarts
      // this.category23.resize(); //重新初始化echarts
      // this.category24.resize(); //重新初始化echarts
    };
  },
  methods: {
    changeDate() {
      this.$forceUpdate();
    },
    editDate(row, index) {
      if (row.show == true) {
        let olds = Object.assign(this.oldMeasureList[index], { show: false });
        this.$set(this.measureList, index, olds);
      } else if (row.show == false) {
        row.show = true;
        this.oldMeasureList[index] = JSON.parse(JSON.stringify(row)); // 复制旧的数据，取消时用到
        this.$set(this.measureList, index, row);
      }
      // 修改后保存
    },
    deleteDate(row, index) {
      const ids = row.indexn;
      this.$confirm(
        this.$t("boarMeasure.sureCancelMeasure") +
          `"` +
          ids +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delMeasure(ids);
        })
        .then(() => {
          this.getListMeasure();
          this.msgSuccess(this.$t("common.delete"));
        });
    },
    handleGather() {
      this.formDayGather.nenddate = formatDate(
        addTime(new Date(this.queryParamsMeasure.nstartdate))
      );
      this.formDayGather.mid = this.queryParamsMeasure.mid;
      this.openDayGather = true;
    },
    handleAddData() {
      if (this.measureList == undefined) {
        this.measureList = new Array();
      }
      let obj = {};
      obj.indexn = this.queryParamsMeasure.indexn;
      obj.mrfid = this.queryParamsMeasure.mrfid;
      obj.mid = this.queryParamsMeasure.mid;
      obj.mfactory = this.$store.state.settings.nowPigFarm;
      obj.show = true;
      this.measureList.push(obj);
    },

    cancelDayGather() {
      this.openDayGather = false;
    },

    submitFormDayGather() {
      this.$refs["formDayGather"].validate((valid) => {
        if (valid) {
          this.formDayGather.mfactory = this.$store.state.settings.nowPigFarm;
          gatherOneDayOnePigData(this.formDayGather).then((response) => {
            this.msgSuccess(this.$t("common.addSuccess"));
            this.openDayGather = false;
            this.getList();
          });
        }
      });
    },

    handleDataSave(scope, index, item) {
      scope.mfactory = this.$store.state.settings.nowPigFarm;
      scope.ningestion =
        Number(scope.nfriweight) +
        Number(scope.ndpc) * Number(scope.nnum) -
        Number(scope.nsecweight);
      updateMeasure(scope).then((response) => {
        this.msgSuccess(this.$t("common.modifiedSuccess"));
        scope.show = scope.show ? false : true;
        Vue.set(item ? item : this.measureList, index, scope);
        this.getListMeasure();
      });
    },
    addDate(date, days) {
      var d = new Date(date);
      d.setDate(d.getDate() + days);
      var m = d.getMonth() + 1;
      return d.getFullYear() + "-" + m + "-" + d.getDate();
    },
    clickTr(row, event, column) {
      this.queryParamsMeasure.nstartdate = row.ndate;
      this.queryParamsMeasure.nenddate = this.addDate(row.ndate, 1);
      this.queryParamsMeasure.mid = row.mid;
      this.queryParamsMeasure.mrfid = row.mrfid;
      this.queryParamsMeasure.indexn = row.indexn;
      this.queryParamsMeasure.ntype = 1;
      this.queryParamsMeasure.mfactory = this.$store.state.settings.nowPigFarm;

      listMeasure(this.queryParamsMeasure).then((response) => {
        this.measureList = response.rows;
        this.measureList.map((i) => {
          // map()适用于你要改变数据值的时候。不仅仅在于它更快，而且返回一个新的数组。这样的优点在于你可以使用复合(composition)(map(), filter(), reduce()等组合使用)来玩出更多的花样
          i.show = false;
          return i;
        });
        this.totalMeasure = response.total;
        this.openMeasure = true;
        this.titleMeasure = this.$t("boarMeasure.viewIndividualFeedIntakeData");
      });
    },
    getListMeasure() {
      listMeasure(this.queryParamsMeasure).then((response) => {
        this.measureList = response.rows;
        this.measureList.map((i) => {
          // map()适用于你要改变数据值的时候。不仅仅在于它更快，而且返回一个新的数组。这样的优点在于你可以使用复合(composition)(map(), filter(), reduce()等组合使用)来玩出更多的花样
          i.show = false;
          return i;
        });
        this.totalMeasure = response.total;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.indexn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = this.$t("boarMeasure.addMeasureday");
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const indexn = row.indexn || this.ids;
      getMeasureday(indexn).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = this.$t("boarMeasure.updateMeasureday");
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.indexn != null) {
            updateMeasureday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.modifiedSuccess"));
              this.open = false;
              this.getList();
            });
          } else {
            this.mfactory = this.$store.state.settings.nowPigFarm;
            addMeasureday(this.form).then((response) => {
              this.msgSuccess(this.$t("common.addSuccess"));
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const indexns = row.indexn || this.ids;
      this.$confirm(
        this.$t("boarMeasure.sureCancelMeasureday") +
          `"` +
          indexns +
          `"` +
          this.$t("boarMeasure.dataItem"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return delMeasureday(indexns);
        })
        .then(() => {
          this.getList();
          this.msgSuccess(this.$t("common.delete"));
        });
    },

    listPigdata() {
      listPigdata({
        nindex: this.queryParams.nindex,
        mfactory: this.$store.state.settings.nowPigFarm,
        ntype: this.queryParams.checked && 1,
      }).then((response) => {
        if (response && !isEmpty(response.rows)) {
          this.mrfidOptions = response.rows;
          this.queryParams.mrfid = this.mrfidOptions[0].mrfid;
          this.queryParams.mid = this.mrfidOptions[0].mid;
          this.getPigDataByMid();
        } else {
          alert(this.$t("boarMeasure.noData"));
          this.mrfidOptions = [];
          this.queryParams.mrfid = null;
          this.queryParams.mid = null;
          this.pigdata = {};
          this.measureDays = null;
          this.recordDays = null;
          this.allNIngestionS = null;
          this.weightGrow = null;
          this.measuredayList = [];
        }
      });
    },
    handleSmall() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index > 0) {
          this.queryParams.nindex = this.nIndexOptions[index - 1].nindex;
        }
      } else {
        this.queryParams.nindex = this.nIndexOptions[0].nindex;
      }
      this.listPigdata();
    },
    handleBig() {
      if (this.queryParams.nindex) {
        let index = this.nIndexOptions.findIndex(
          (item) => item.nindex == this.queryParams.nindex
        );
        if (index < this.nIndexOptions.length - 1) {
          this.queryParams.nindex = this.nIndexOptions[index + 1].nindex;
        }
      } else {
        this.queryParams.nindex =
          this.nIndexOptions[this.nIndexOptions.length - 1].nindex;
      }
      this.listPigdata();
    },
    handleSmallRfid() {
      if (this.queryParams.mrfid) {
        let index = this.mrfidOptions.findIndex(
          (item) => item.mrfid == this.queryParams.mrfid
        );
        if (index > 0) {
          this.queryParams.mrfid = this.mrfidOptions[index - 1].mrfid;
          // this.queryParams.dateRange = [
          //   new Date(
          //     this.mrfidOptions[index - 1] &&
          //       this.mrfidOptions[index - 1].dstartdate
          //   ),
          //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
          // ];
        }
      } else {
        this.queryParams.mrfid = this.mrfidOptions[0].mrfid;
        // this.queryParams.dateRange = [
        //   new Date(this.mrfidOptions[0] && this.mrfidOptions[0].dstartdate),
        //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
        // ];
      }
      this.queryParams.mid = null;
      //this.getPigDataByMid();
    },
    handleBigRfid() {
      if (this.queryParams.mrfid) {
        let index = this.mrfidOptions.findIndex(
          (item) => item.mrfid == this.queryParams.mrfid
        );
        if (index < this.mrfidOptions.length - 1) {
          this.queryParams.mrfid = this.mrfidOptions[index + 1].mrfid;
          // this.queryParams.dateRange = [
          //   new Date(
          //     this.mrfidOptions[index + 1] &&
          //       this.mrfidOptions[index + 1].dstartdate
          //   ),
          //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
          // ];
        }
      } else {
        this.queryParams.mrfid =
          this.mrfidOptions[this.mrfidOptions.length - 1].mrfid;
        // this.queryParams.dateRange = [
        //   new Date(
        //     this.mrfidOptions[this.mrfidOptions.length - 1] &&
        //       this.mrfidOptions[this.mrfidOptions.length - 1].dstartdate
        //   ),
        //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
        // ];
      }
      this.queryParams.mid = null;
      //this.getPigDataByMid();
    },
    handleSmallMid() {
      if (this.queryParams.mid) {
        let index = this.mrfidOptions.findIndex(
          (item) => item.mid == this.queryParams.mid
        );
        if (index > 0) {
          this.queryParams.mid = this.mrfidOptions[index - 1].mid;
          // this.queryParams.dateRange = [
          //   new Date(
          //     this.mrfidOptions[index - 1] &&
          //       this.mrfidOptions[index - 1].dstartdate
          //   ),
          //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
          // ];
        }
      } else {
        this.queryParams.mid = this.mrfidOptions[0].mid;
        // this.queryParams.dateRange = [
        //   new Date(this.mrfidOptions[0] && this.mrfidOptions[0].dstartdate),
        //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
        // ];
      }
      this.queryParams.mrfid = null;
      //this.getPigDataByMid();
    },
    handleBigMid() {
      if (this.queryParams.mid) {
        let index = this.mrfidOptions.findIndex(
          (item) => item.mid == this.queryParams.mid
        );
        if (index < this.mrfidOptions.length - 1) {
          this.queryParams.mid = this.mrfidOptions[index + 1].mid;
          // this.queryParams.dateRange = [
          //   new Date(
          //     this.mrfidOptions[index + 1] &&
          //       this.mrfidOptions[index + 1].dstartdate
          //   ),
          //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
          // ];
        }
      } else {
        this.queryParams.mid =
          this.mrfidOptions[this.mrfidOptions.length - 1].mid;
        // this.queryParams.dateRange = [
        //   new Date(
        //     this.mrfidOptions[this.mrfidOptions.length - 1] &&
        //       this.mrfidOptions[this.mrfidOptions.length - 1].dstartdate
        //   ),
        //   new Date(new Date().getTime() - 24 * 60 * 60 * 1000),
        // ];
      }
      this.queryParams.mrfid = null;
      //this.getPigDataByMid();
    },
    handleMrfid() {
      this.queryParams.pageNum = 1;
      this.queryParams.mid = null;
      //this.getPigDataByMid();
    },
    handleMid(val) {
      this.queryParams.pageNum = 1;
      this.queryParams.mrfid = null;
      //this.$nextTick(() => {
      //  this.getPigDataByMid();
      //})
    },
    handleClick(tab, event) {
      if (tab.name == "first") this.getList();
      if (tab.name == "second") {
        this.$nextTick(() => {
          this.getPigDataByMid();
          // this.category22.resize();
        });
      }
      if (tab.name == "third") {
        this.$nextTick(() => {
          this.getPigDataByMid();
          // this.category23.resize();
        });
      }
      if (tab.name == "four") {
        this.$nextTick(() => {
          this.getPigDataByMid();
          // this.category24.resize();
        });
      }
    },

    Subtr(arg1, arg2) {
      var r1, r2, m, n;
      try {
        r1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      n = r1 >= r2 ? r1 : r2;
      return ((arg1 * m - arg2 * m) / m).toFixed(n);
    },

    // 将数值除以1000，同时考虑浮点数精度问题、整数溢出和除数为0的情况
    divideBy1000(number) {
      if (number === 0) {
        return 0; // 返回 NaN 表示无效的结果
      }

      // 将数值转换为字符串，以便处理较大的整数
      const numberString = number.toString();
      // 如果原始数值包含小数点，则将小数点后的部分舍去
      const integerPart = numberString.split(".")[0];

      // 如果原始数值是整数且小于Number.MAX_SAFE_INTEGER，则直接除以1000
      // 如果原始数值是整数且小于Number.MAX_SAFE_INTEGER，则直接除以1000
      if (Number.isSafeInteger(number) && number < Number.MAX_SAFE_INTEGER) {
        return number / 1000;
      } else {
        // 如果原始数值较大，使用字符串操作来避免精度丢失和整数溢出
        const dividedString =
          integerPart.length > 3
            ? integerPart.slice(0, -3) + "." + integerPart.slice(-3)
            : "0." + integerPart.padStart(3, "0");

        return parseFloat(dividedString);
      }
    },

    getFoodCharts() {
      getFoodCharts(
        this.addDateRangeRe(
          {
            mfactory: this.$store.state.settings.nowPigFarm,
            mid: this.pigdata.mid,
            // pageNum: this.queryParams.pageNum,
            // pageSize: this.queryParams.pageSize,
          },
          this.queryParams.dateRange
        )
      ).then((response) => {
        this.foodEchartList = response.rows;

        this.foodEchartList.forEach((element) => {
          this.xDataFood = element.xAries.reverse();
          this.yDataFood = element.yAries.reverse();
        });
        var sum = 0;
        this.yDataFood = this.yDataFood.map((element) => {
          return this.divideBy1000(element);
        });
        this.yDataFood.forEach((ele) => {
          sum = sum + Number(ele);
        });
        this.allNIngestionS = sum;

        this.getFoodDatas();
      });
    },
    getWeightCharts() {
      this.lastweight = 0;
      this.firstweight = 0;

      getWeightCharts(
        this.addDateRangeRe(
          {
            mfactory: this.$store.state.settings.nowPigFarm,
            mid: this.pigdata.mid,
            // pageNum: this.queryParams.pageNum,
            // pageSize: this.queryParams.pageSize,
          },
          this.queryParams.dateRange
        )
      ).then((response) => {
        this.weightEchartList = response.rows;

        this.weightEchartList.forEach((element) => {
          this.xWeight = element.xAries.reverse();
          this.yWeight = element.yAries.reverse();
          this.lastweight = this.yWeight[element.yAries.length - 1];
          this.firstweight = this.yWeight[0];
        });
        this.weightGrow =
          this.lastweight &&
          this.firstweight &&
          this.Subtr(this.lastweight, this.firstweight);

        this.getWeightDatas(); //必须放这，放其它地方有bug
        // this.weightGrow =
        //   this.lastweight &&
        //   this.firstweight &&
        //   this.Subtr(this.lastweight, this.firstweight);
        // Number(lastweight) - Number(this.pigdata.nweight1);
      });
    },
    getPigweightGrowCharts() {
      getPigweightGrowCharts(
        this.addDateRangeRe(
          {
            mfactory: this.$store.state.settings.nowPigFarm,
            mid: this.pigdata.mid,
            // pageNum: this.queryParams.pageNum,
            // pageSize: this.queryParams.pageSize,
          },
          this.queryParams.dateRange
        )
      ).then((response) => {
        this.weightGrowList = response.rows;

        this.weightGrowList.forEach((element) => {
          this.xWeightGrow = element.xAries;
          this.yWeightGrow = element.yAries;
        });

        this.getPigweightGrowDatas();
      });
    },

    getDateRangeByweight() {
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      //清空日期
      this.queryParams.dateRange = null;
      this.queryParams.datefrom = null;
      this.queryParams.dateto = null;
      // this.queryParams.activeQueryType = this.activeQueryType;

      if (this.queryParams && this.queryParams.mid) {
        getdaterangebyweightrange(this.queryParams).then((response) => {
          //this.queryParams.dateRange = Array.of(new Date(response.data.datefrom),new Date(response.data.dateto)) ;
          this.queryParams.dateRange = Array.of(
            response.data.datefrom,
            response.data.dateto
          );
        });
      }
    },

    getDateRangeByRl() {
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      //清空日期
      this.queryParams.dateRange = null;
      this.queryParams.datefrom = null;
      this.queryParams.dateto = null;
      // this.queryParams.activeQueryType = this.activeQueryType;

      if (this.queryParams && this.queryParams.mid) {
        getdaterangebyrl(this.queryParams).then((response) => {
          //this.queryParams.dateRange = Array.of(new Date(response.data.datefrom),new Date(response.data.dateto)) ;
          this.queryParams.dateRange = Array.of(
            response.data.datefrom,
            response.data.dateto
          );
        });
      }
    },

    /** 查询【个体数据】列表 */
    getPigDataByMid() {
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;
      if (
        this.queryParams &&
        (this.queryParams.mid || this.queryParams.mrfid)
      ) {
        listPigdata({
          mfactory: this.$store.state.settings.nowPigFarm,
          mid: this.queryParams.mid,
          mrfid: this.queryParams.mrfid,
          // nindex: this.queryParams.nindex,
        }).then((response) => {
          if (response.rows[0] == null) {
            alert(
              this.queryParams.mid
                ? this.$t("boarMeasure.mid") +
                    this.queryParams.mid +
                    this.$t("boarMeasure.noRecords")
                : this.$t("boarMeasure.mrfid") +
                    this.queryParams.mrfid +
                    this.$t("boarMeasure.noRecords")
            );
          } else {
            this.pigdata = response.rows[0];

            //计算记录天数
            var totay = new Date();
            var beginDay = new Date(this.pigdata.dstartdate);
            var endDay = null;
            if (this.pigdata.ntype != 0) endDay = totay;
            else endDay = new Date(this.pigdata.denddate);

            this.measureDays = getDaysDiffBetweenDates(
              new Date(formatDay(beginDay)),
              new Date(formatDay(endDay))
            );
            // this.queryParams.mrfid = this.pigdata.mrfid;
            // this.queryParams.mid = null;
            this.getList();
            this.getFoodCharts();
            this.getWeightCharts();
            // this.getPigweightGrowCharts();

            //this.category23.dispose(); //销毁
            //this.getWeightDatas();
          }

          //alert(this.xDataFood);
        });
      } else {
        this.msgInfo(this.$t("boarMeasure.pleaseEnterColumnFirst"));
      }
    },
    /** 查询【测定日报告】列表 */
    getList() {
      this.loading = true;
      this.queryParams.mfactory = this.$store.state.settings.nowPigFarm;

      listMeasureday(
        this.addDateRangeRe(
          {
            mfactory: this.$store.state.settings.nowPigFarm,
            mid: this.pigdata.mid,
            pageNum: this.queryParams.pageNum,
            pageSize: this.queryParams.pageSize,
          },
          this.queryParams.dateRange
        )
      )
        .then((response) => {
          this.measuredayList = response.rows;
          this.total = response.total;
          this.recordDays = response.total;
        })
        .catch((error) => {
          console.error("获取测定日报告列表失败:", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        indexn: null,
        mid: null,
        mrfid: null,
        mdorm: null,
        nindex: null,
        mname: null,
        naddress: null,
        ndate: null,
        ningestions: null,
        nfeednum: null,
        nseconds: null,
        nmidweight: null,
        //测定天数
        measureDays: null,
        //记录天数
        recordDays: null,
        //总采食量
        allNIngestionS: null,
        //总增重
        weightGrow: null,

        pigdata: null,
        //采食echart
        category22: null,
        //体重echart
        category23: null,
        category24: null,
        //采食x轴数据
        xDataFood: [],
        //采食y轴数据
        yDataFood: [],
        //体重x轴数据
        xWeight: [],
        //体重y轴数据
        yWeight: [],
        //foodEchart
        foodEchartList: [],
        //weightEchart
        weightEchartList: [],
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      // this.getList();
      this.getPigDataByMid();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.activeQueryType = "date";
      // this.queryParams = {
      //   dateRange: null,
      //   weightFrom: null,
      //   weightTo: null,
      //   rlFrom: null,
      //   rlTo: null,
      // };
      this.resetForm("queryForm");
      this.handleQuery();
      // listPigdata({
      //   mfactory: this.$store.state.settings.nowPigFarm,
      // }).then((response) => {
      //   this.mrfidOptions = response.rows;
      // });
    },

    /** 导出按钮操作 */
    handleExport() {
      const queryParams = { ...this.queryParams };
      queryParams.nindex = null;
      this.$confirm(
        this.$t("boarMeasure.sureExportDayReport"),
        this.$t("common.warn"),
        {
          confirmButtonText: this.$t("common.determine"),
          cancelButtonText: this.$t("common.cancel"),
          type: "warning",
        }
      )
        .then(function () {
          return exportMeasureday(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
    getFoodDatas() {
      if (
        this.category22 != null &&
        this.category22 != "" &&
        this.category22 != undefined
      ) {
        this.category22.dispose(); //销毁
      }
      this.category22 = this.$refs.category22
        ? echarts.init(this.$refs.category22, "walden")
        : "";
      this.category22 &&
        this.category22.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              // 坐标轴指示器，坐标轴触发有效
              type: "shadow", // 默认为直线，可选为：'line' | 'shadow'
            },
          },
          legend: {
            data: [this.$t("boarMeasure.ningestion")],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            data: this.xDataFood,
          },
          yAxis: {
            type: "value",
            axisLabel: {
              formatter: function (value) {
                // console.log("value", this.divideBy1000(value));
                // 对数值进行自定义格式化，例如添加单位
                return value;
              },
            },
          },
          series: [
            {
              name: this.$t("boarMeasure.ningestionKG"),
              type: "bar",
              // stack: "总量",
              barWidth: "40%",
              data: this.yDataFood,
            },
          ],
        });
    },
    getWeightDatas() {
      if (this.category23) {
        this.category23.dispose(); //销毁
      }
      this.$nextTick(() => {
        this.category23 = this.$refs.category23
          ? echarts.init(this.$refs.category23, "walden")
          : "";

        this.category23 &&
          this.category23.setOption({
            title: {
              text: "",
              left: "center",
            },
            tooltip: {
              trigger: "axis",
            },
            legend: {
              data: [this.$t("boarMeasure.weight")],
            },
            grid: {
              left: "4%",
              right: "4%",
              bottom: "3%",
              containLabel: true,
            },
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            xAxis: {
              type: "category",
              axisTick: {
                alignWithLabel: true,
              },
              boundaryGap: false,
              data: this.xWeight,
            },
            yAxis: {
              type: "value",
              maxInterval: 5,
            },
            series: [
              {
                // symbol: "none", //取消折点圆圈
                name: this.$t("boarMeasure.weightKg"),
                type: "line",
                // stack: "体重",
                data: this.yWeight,
              },
            ],
          });
      });
    },

    getPigweightGrowDatas() {
      if (
        this.category24 != null &&
        this.category24 != "" &&
        this.category24 != undefined
      ) {
        this.category24.dispose(); //销毁
      }
      this.category24 = this.$refs.category24
        ? echarts.init(this.$refs.category24, "walden")
        : "";
      this.category24 &&
        this.category24.setOption({
          title: {
            text: "",
          },
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [this.$t("boarMeasure.dailyGainWeight")],
          },
          grid: {
            left: "4%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          xAxis: {
            type: "category",
            axisTick: {
              alignWithLabel: true,
            },
            boundaryGap: false,
            data: this.xWeightGrow,
          },
          yAxis: {
            type: "value",
          },
          series: [
            {
              symbol: "none", //取消折点圆圈
              name: this.$t("boarMeasure.dailyGainWeightKg"),
              type: "line",
              // stack: "日增重",
              data: this.yWeightGrow,
            },
          ],
        });
    },
    // 初始化数据
    initializeData() {
      listControl({
        ntype: 2,
        mfactory: this.$store.state.settings.nowPigFarm,
      }).then((response) => {
        this.nIndexOptions = response.rows;
        if (!(JSON.stringify(this.$route.query) === "{}")) {
          this.queryParams.nindex = this.$route.query.nindex;
        } else {
          this.queryParams.nindex = this.nIndexOptions[0].nindex;
        }
        listPigdata({
          nindex: this.queryParams.nindex,
          mfactory: this.$store.state.settings.nowPigFarm,
          ntype: this.queryParams.checked && 1,
        }).then((response) => {
          this.mrfidOptions = response.rows;
          if (!(JSON.stringify(this.$route.query) === "{}")) {
            this.queryParams.mid = this.$route.query.mid;
          } else {
            this.queryParams.mrfid =
              this.mrfidOptions[0] && this.mrfidOptions[0].mrfid;
            this.queryParams.mid =
              this.mrfidOptions[0] && this.mrfidOptions[0].mid;
          }
          this.getPigDataByMid();
        });
      });
    },
    // 保存查询条件到sessionStorage
    saveQueryParams() {
      const cacheKey = "measureday_query_params";
      const queryParamsToSave = {
        activeQueryType: this.activeQueryType,
        checked: this.queryParams.checked,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        mid: this.queryParams.mid,
        mrfid: this.queryParams.mrfid,
        nindex: this.queryParams.nindex,
        rlFrom: this.queryParams.rlFrom,
        rlTo: this.queryParams.rlTo,
        weightFrom: this.queryParams.weightFrom,
        weightTo: this.queryParams.weightTo,
        dateRange: this.queryParams.dateRange,
      };
      sessionStorage.setItem(cacheKey, JSON.stringify(queryParamsToSave));
    },
    // 从sessionStorage恢复查询条件
    restoreQueryParams() {
      const cacheKey = "measureday_query_params";
      const cachedParams = sessionStorage.getItem(cacheKey);
      if (cachedParams) {
        try {
          const parsedParams = JSON.parse(cachedParams);
          this.activeQueryType = parsedParams.activeQueryType || "date";
          console.log("parsedParams", parsedParams);
          // 先获取nIndexOptions数据
          listControl({
            ntype: 2,
            mfactory: this.$store.state.settings.nowPigFarm,
          }).then((response) => {
            this.nIndexOptions = response.rows;

            // 恢复nindex
            if (parsedParams.nindex) {
              this.queryParams.nindex = parsedParams.nindex;
            } else {
              this.queryParams.nindex = this.nIndexOptions[0]?.nindex;
            }

            // 获取mrfidOptions数据
            listPigdata({
              nindex: this.queryParams.nindex,
              mfactory: this.$store.state.settings.nowPigFarm,
              ntype: parsedParams.checked && 1,
            }).then((response) => {
              this.mrfidOptions = response.rows;

              // 恢复所有查询条件
              Object.keys(parsedParams).forEach((key) => {
                if (
                  parsedParams[key] !== null &&
                  parsedParams[key] !== undefined
                ) {
                  this.queryParams[key] = parsedParams[key];
                }
              });

              // 确保mid和mrfid有效
              // if (
              //   !this.queryParams.mid ||
              //   !this.mrfidOptions.find(
              //     (item) => item.mid === this.queryParams.mid
              //   )
              // ) {
              //   this.queryParams.mrfid = this.mrfidOptions[0]?.mrfid;
              //   this.queryParams.mid = this.mrfidOptions[0]?.mid;
              // }

              // 恢复后重新获取数据
              this.getPigDataByMid();
            });
          });
        } catch (error) {
          console.error("恢复查询条件失败:", error);
          // 如果恢复失败，执行正常的初始化
          this.initializeData();
        }
      } else {
        this.initializeData();
      }
    },
    // 格式化行数据为整数（去掉小数位）
    formatRowToThreeDecimals(row, field) {
      if (row[field] && !isNaN(row[field])) {
        row[field] = Math.round(Number(row[field]));
      }
    },
    /** 处理日龄范围输入 */
    handleAgeRangeInput() {
      // 清除之前的定时器
      if (this.rlRangeTimer) {
        clearTimeout(this.rlRangeTimer);
      }
      // 设置防抖，800ms后执行查询
      this.rlRangeTimer = setTimeout(() => {
        if (this.validateAgeRange(false)) {
          this.getDateRangeByRl();
        }
      }, 800);
    },

    /** 处理体重范围输入 */
    handleWeightRangeInput() {
      // 清除之前的定时器
      if (this.weightRangeTimer) {
        clearTimeout(this.weightRangeTimer);
      }
      // 设置防抖，800ms后执行查询
      this.weightRangeTimer = setTimeout(() => {
        if (this.validateWeightRange(false)) {
          this.getDateRangeByweight();
        }
      }, 800);
    },

    /** 验证日龄范围 */
    validateAgeRange(showMessage = true) {
      const { rlFrom, rlTo } = this.queryParams;

      // 检查是否为空
      if (!rlFrom || !rlTo) {
        if (showMessage && (rlFrom || rlTo)) {
          this.$message.warning("请输入完整的日龄范围");
        }
        return false;
      }

      // 转换为数字
      const fromNum = Number(rlFrom);
      const toNum = Number(rlTo);

      // 检查是否为有效数字
      if (isNaN(fromNum) || isNaN(toNum)) {
        if (showMessage) {
          this.$message.error("日龄必须为有效数字");
        }
        return false;
      }

      // 检查是否为负数
      if (fromNum < 0 || toNum < 0) {
        if (showMessage) {
          this.$message.error("日龄不能为负数");
        }
        return false;
      }

      // 检查大小关系
      if (fromNum >= toNum) {
        if (showMessage) {
          this.$message.error("结束日龄必须大于开始日龄");
        }
        return false;
      }

      return true;
    },

    /** 验证体重范围 */
    validateWeightRange(showMessage = true) {
      const { weightFrom, weightTo } = this.queryParams;

      // 检查是否为空
      if (!weightFrom || !weightTo) {
        if (showMessage && (weightFrom || weightTo)) {
          this.$message.warning("请输入完整的体重范围");
        }
        return false;
      }

      // 转换为数字
      const fromNum = Number(weightFrom);
      const toNum = Number(weightTo);

      // 检查是否为有效数字
      if (isNaN(fromNum) || isNaN(toNum)) {
        if (showMessage) {
          this.$message.error("体重必须为有效数字");
        }
        return false;
      }

      // 检查是否为负数
      if (fromNum < 0 || toNum < 0) {
        if (showMessage) {
          this.$message.error("体重不能为负数");
        }
        return false;
      }

      // 检查大小关系
      if (fromNum >= toNum) {
        if (showMessage) {
          this.$message.error("结束体重必须大于开始体重");
        }
        return false;
      }

      return true;
    },

    // 查询类型变化处理
    handleQueryTypeChange(activeType) {
      // 清空所有查询条件
      this.queryParams.rlFrom = null;
      this.queryParams.rlTo = null;
      this.queryParams.weightFrom = null;
      this.queryParams.weightTo = null;

      console.log("查询类型切换为:", activeType);
    },
  },
};
</script>

<style scoped>
/* 让特定表单项占满一整行 */
.full-line {
  width: 100%;
}
</style>
